# Настройка Telegram бота

## Шаг 1: Создание бота

1. Откройте Telegram и найдите @BotFather
2. Отправьте команду `/start`
3. Отправьте команду `/newbot`
4. Введите имя для вашего бота (например: "Ticket Price Monitor")
5. Введите username для бота (должен заканчиваться на "bot", например: "ticket_price_monitor_bot")
6. Сохраните полученный токен (выглядит как: `1234567890:ABCdefGHIjklMNOpqrsTUVwxyz`)

## Шаг 2: Получение Chat ID

### Для личного чата:
1. Найдите вашего бота в Telegram по username
2. Отправьте боту любое сообщение (например: `/start`)
3. Откройте в браузере: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   (замените `<YOUR_BOT_TOKEN>` на ваш токен)
4. Найдите в JSON ответе поле `"chat"` -> `"id"` - это ваш Chat ID

### Для группового чата:
1. Добавьте бота в группу
2. Отправьте в группе любое сообщение
3. Откройте в браузере: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. Найдите Chat ID группы (будет отрицательным числом)

## Шаг 3: Настройка конфигурации

Отредактируйте файл `config/config.yml`:

```yaml
telegram:
  bot_token: "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"  # Ваш токен
  chat_ids: [123456789, -987654321]  # Ваши Chat ID (можно несколько)
```

## Шаг 4: Тестирование

1. Запустите управление подписчиками:
```bash
ruby manage_subscribers.rb
```

2. Выберите пункт 4 для отправки тестового сообщения

3. Проверьте, что сообщение пришло в Telegram

## Пример настройки

```yaml
telegram:
  bot_token: "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
  chat_ids: [123456789]  # Ваш личный Chat ID
```

## Управление подписчиками

Используйте скрипт `manage_subscribers.rb` для:
- Просмотра текущих подписчиков
- Добавления новых Chat ID
- Удаления подписчиков
- Отправки тестовых сообщений

## Безопасность

- Никогда не публикуйте токен бота в открытом доступе
- Храните токен в безопасном месте
- При компрометации токена создайте нового бота
