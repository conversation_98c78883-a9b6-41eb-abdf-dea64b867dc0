# 🚀 Новые возможности: Кеширование и Интерактивное меню

## 🗄️ Кеширование цен

### Что это дает:
- **⚡ Быстрые ответы** - повторные запросы цен выполняются мгновенно
- **🔄 Снижение нагрузки** на API bgoperator.ru
- **⏰ Актуальность данных** - кеш автоматически обновляется каждые 15 минут

### Как работает:
1. При первом запросе цены загружаются с сайта и сохраняются в кеш
2. Повторные запросы в течение 15 минут берутся из кеша
3. Устаревшие данные автоматически удаляются
4. Пользователь видит, когда данные взяты из кеша

### Настройка кеша:
В файле `config/config.yml`:
```yaml
cache:
  enabled: true          # Включить/выключить кеширование
  ttl_minutes: 15        # Время жизни кеша в минутах
  file_path: 'cache/price_cache.json'  # Путь к файлу кеша
```

### Управление кешем:
- **Просмотр**: команда `/cache` или кнопка "🗄️ Кеш"
- **Очистка**: кнопка "🗑️ Очистить кеш" в меню кеша
- **Автоматическая очистка**: устаревшие записи удаляются автоматически

## 🎛️ Интерактивное меню

### Главное меню:
Доступно через команды `/start`, `/menu` или при неизвестной команде.

**Кнопки главного меню:**
- **📊 Текущие цены** - получить актуальные цены (с кешированием)
- **📈 Статус** - проверить статус подписки и количество подписчиков
- **🔔 Подписаться** - подписаться на автоматические уведомления
- **🔕 Отписаться** - отписаться от уведомлений
- **🗄️ Кеш** - информация о кеше и управление им
- **❓ Помощь** - справка по всем командам

### Меню кеша:
Доступно через кнопку "🗄️ Кеш" в главном меню.

**Кнопки меню кеша:**
- **🗑️ Очистить кеш** - принудительно очистить весь кеш
- **🔙 Главное меню** - вернуться к главному меню

### Преимущества интерактивного меню:
- **Удобство** - не нужно запоминать команды
- **Наглядность** - все функции видны сразу
- **Быстрота** - один клик вместо ввода команды
- **Мобильность** - удобно на мобильных устройствах

## 📱 Примеры использования

### Получение цен с кешированием:
1. Нажмите "📊 Текущие цены"
2. При первом запросе: данные загружаются с сайта (3-10 секунд)
3. При повторном запросе: данные берутся из кеша (мгновенно)
4. Сообщение показывает источник данных

### Управление кешем:
1. Нажмите "🗄️ Кеш" в главном меню
2. Посмотрите информацию о кешированных данных
3. При необходимости нажмите "🗑️ Очистить кеш"
4. Вернитесь в главное меню кнопкой "🔙 Главное меню"

### Подписка на уведомления:
1. Нажмите "🔔 Подписаться" в главном меню
2. Получите подтверждение подписки
3. Проверьте статус кнопкой "📈 Статус"
4. При необходимости отпишитесь кнопкой "🔕 Отписаться"

## 🔧 Технические детали

### Структура кеша:
```json
{
  "VVO_HKT_27.06": {
    "price": 26339,
    "timestamp": "2025-06-24T17:38:03+10:00"
  },
  "VVO_BKK_30.06": {
    "price": 28500,
    "timestamp": "2025-06-24T17:35:15+10:00"
  }
}
```

### Файлы системы:
- `lib/price_cache.rb` - класс для работы с кешем
- `cache/price_cache.json` - файл с кешированными данными
- `lib/telegram_bot.rb` - обновленный бот с интерактивным меню

### Обратная совместимость:
- Все старые команды продолжают работать
- Можно использовать как команды, так и кнопки
- Настройки кеша опциональны (можно отключить)

## 🧪 Тестирование

### Тест кеширования:
```bash
ruby test_cache.rb
```

### Тест бота:
```bash
ruby -c lib/telegram_bot.rb
ruby -c lib/price_cache.rb
```

### Проверка синтаксиса:
```bash
ruby -c bot.rb
ruby -c lib/price_parser.rb
```

## 🚀 Запуск с новыми возможностями

### Только бот:
```bash
ruby bot.rb
```

### Бот + мониторинг:
```bash
ruby run_service.rb
# Выберите пункт 4
```

### Разовая проверка с кешем:
```bash
ruby main.rb
```

Теперь система работает быстрее и удобнее! 🎉
