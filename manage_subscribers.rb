#!/usr/bin/env ruby

require_relative 'lib/telegram_bot'

class Subscriber<PERSON>anager
  def initialize
    @notifier = TelegramNotifier.new
  end

  def run
    puts "📱 Управление подписчиками Telegram бота"
    puts "=" * 40

    loop do
      puts "\nВыберите действие:"
      puts "1. Показать текущих подписчиков"
      puts "2. Добавить подписчика"
      puts "3. Удалить подписчика"
      puts "4. Отправить тестовое сообщение"
      puts "0. Выход"
      
      print "\nВведите номер действия: "
      choice = gets.chomp

      case choice
      when '1'
        show_subscribers
      when '2'
        add_subscriber
      when '3'
        remove_subscriber
      when '4'
        send_test_message
      when '0'
        puts "До свидания!"
        break
      else
        puts "❌ Неверный выбор"
      end
    end
  end

  private

  def show_subscribers
    config = YAML.load_file('config/config.yml')
    chat_ids = config['telegram']['chat_ids']
    
    if chat_ids.empty?
      puts "📭 Нет подписчиков"
    else
      puts "📋 Текущие подписчики:"
      chat_ids.each_with_index do |chat_id, index|
        puts "  #{index + 1}. #{chat_id}"
      end
    end
  end

  def add_subscriber
    print "Введите Chat ID для добавления: "
    chat_id = gets.chomp.to_i
    
    if chat_id == 0
      puts "❌ Неверный Chat ID"
      return
    end

    if @notifier.add_subscriber(chat_id)
      puts "✅ Подписчик #{chat_id} добавлен"
    else
      puts "⚠️  Подписчик #{chat_id} уже существует"
    end
  end

  def remove_subscriber
    show_subscribers
    print "\nВведите Chat ID для удаления: "
    chat_id = gets.chomp.to_i
    
    if chat_id == 0
      puts "❌ Неверный Chat ID"
      return
    end

    if @notifier.remove_subscriber(chat_id)
      puts "✅ Подписчик #{chat_id} удален"
    else
      puts "⚠️  Подписчик #{chat_id} не найден"
    end
  end

  def send_test_message
    test_data = [
      {
        from: "VVO",
        to: "HKT", 
        date_str: "27.06",
        price: 25000
      },
      {
        from: "VVO",
        to: "BKK",
        date_str: "30.06", 
        price: 30000
      }
    ]

    if @notifier.send_price_report(test_data)
      puts "✅ Тестовое сообщение отправлено"
    else
      puts "❌ Ошибка отправки тестового сообщения"
    end
  end
end

if __FILE__ == $0
  manager = SubscriberManager.new
  manager.run
end
