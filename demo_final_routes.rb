#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'

puts "🌍 ФИНАЛЬНАЯ ДЕМОНСТРАЦИЯ ВСЕХ МАРШРУТОВ"
puts "=" * 70

route_manager = RouteManager.new
price_parser = PriceParser.new

routes = route_manager.get_valid_routes

puts "📊 ОБЩАЯ СТАТИСТИКА:"
puts "🛫 Всего маршрутов: #{routes.length}"
puts

# Группируем по городам отправления
routes_by_origin = routes.group_by { |r| r[:from] }

origins = {
  'VVO' => '🌊 Владивосток',
  'SVO' => '🏛️ Москва (Шереметьево)'
}

destinations = {
  'HKT' => '🏝️ Пхукет, Таиланд',
  'BKK' => '🏙️ Бангкок, Таиланд',
  'MLE' => '🏝️ Мальдивы',
  'SEZ' => '🏖️ Сейшелы'
}

routes_by_origin.each do |origin_code, origin_routes|
  origin_name = origins[origin_code] || origin_code
  puts "#{origin_name}: #{origin_routes.length} маршрутов"
  
  # Группируем по направлениям
  dest_groups = origin_routes.group_by { |r| r[:to] }
  dest_groups.each do |dest_code, dest_routes|
    dest_name = destinations[dest_code] || dest_code
    puts "  → #{dest_name}: #{dest_routes.length} маршрутов"
  end
  puts
end

puts "=" * 70
puts "🧪 ТЕСТИРОВАНИЕ ПРЕДСТАВИТЕЛЕЙ КАЖДОГО НАПРАВЛЕНИЯ"
puts "=" * 70

# Берем по одному маршруту из каждой комбинации город-направление
test_routes = []
routes_by_origin.each do |origin_code, origin_routes|
  dest_groups = origin_routes.group_by { |r| r[:to] }
  dest_groups.each do |dest_code, dest_routes|
    test_routes << dest_routes.first
  end
end

test_routes.each_with_index do |route, index|
  origin_name = origins[route[:from]] || route[:from]
  dest_name = destinations[route[:to]] || route[:to]
  
  puts "\n[#{index + 1}/#{test_routes.length}] 🧪 #{origin_name} → #{dest_name}"
  puts "📍 Маршрут: #{route[:from]}-#{route[:to]} на #{route[:date_str]}"
  puts "⏰ Время: #{Time.now.strftime('%H:%M:%S')}"
  
  begin
    start_time = Time.now
    price = price_parser.get_price(route)
    end_time = Time.now
    
    duration = (end_time - start_time).round(2)
    
    if price
      formatted_price = price.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse
      puts "✅ Цена: #{formatted_price} руб."
      puts "⏱️  Время: #{duration} сек"
      
      if duration < 0.1
        puts "🗄️  Источник: кеш"
      else
        puts "🌐 Источник: API bgoperator.ru"
      end
    else
      puts "❌ Цена не найдена"
      puts "⏱️  Время: #{duration} сек"
    end
    
  rescue => e
    puts "❌ Ошибка: #{e.message}"
  end
  
  # Пауза между запросами (кроме последнего)
  if index < test_routes.length - 1
    puts "⏳ Пауза 3 секунды..."
    sleep(3)
  end
end

puts "\n" + "=" * 70
puts "📋 ИНФОРМАЦИЯ О КЕШЕ"
puts "=" * 70

puts price_parser.get_cache_info

puts "\n" + "=" * 70
puts "🎯 ИТОГОВАЯ СВОДКА ПО НАПРАВЛЕНИЯМ"
puts "=" * 70

puts "✅ ОТЛИЧНО РАБОТАЮТ:"
puts "  🌊 Владивосток → 🏝️ Пхукет (VVO-HKT)"
puts "  🌊 Владивосток → 🏙️ Бангкок (VVO-BKK)"
puts "  🏛️ Москва → 🏝️ Мальдивы (SVO-MLE)"
puts "  🏛️ Москва → 🏖️ Сейшелы (SVO-SEZ)"

puts "\n💰 ПРИМЕРНЫЕ ЦЕНЫ:"
puts "  🏝️ Пхукет из Владивостока: от 18,673 руб."
puts "  🏙️ Бангкок из Владивостока: от 26,339 руб."
puts "  🏝️ Мальдивы из Москвы: от 43,831 руб."
puts "  🏖️ Сейшелы из Москвы: от 48,782 руб."

puts "\n🚀 ПРЕИМУЩЕСТВА СИСТЕМЫ:"
puts "  ⚡ Кеширование на 15 минут для быстрых ответов"
puts "  🎛️ Интерактивное меню в Telegram боте"
puts "  🔄 Автоматический мониторинг каждые 2 часа"
puts "  📱 Уведомления всем подписчикам"
puts "  🗄️ Прозрачность источника данных (кеш/API)"

puts "\n🎯 РЕКОМЕНДАЦИИ:"
puts "  • Используйте московские маршруты для экзотических направлений"
puts "  • Владивостокские маршруты отлично подходят для Таиланда"
puts "  • Подпишитесь на уведомления для отслеживания изменений цен"
puts "  • Проверяйте актуальность на сайте bgoperator.ru перед покупкой"

puts "\n🚀 ЗАПУСК ПОЛНОГО МОНИТОРИНГА:"
puts "  ruby run_service.rb → выберите пункт 4"

puts "\n" + "=" * 70
puts "🎉 СИСТЕМА ГОТОВА К РАБОТЕ С 16 МАРШРУТАМИ!"
puts "✈️ Приятных путешествий! 🌴🏖️🏝️"
puts "=" * 70
