#!/usr/bin/env ruby

require_relative 'lib/simple_price_parser'
require 'yaml'
require 'date'

# Включаем отладку
config = YAML.load_file('config/config.yml')
config['debug'] = true
File.write('config/config.yml', config.to_yaml)

parser = SimplePriceParser.new

# Тестируем с данными маршрута
test_route = {
  from: "VVO",
  to: "HKT",
  date: Date.new(2025, 7, 4),
  date_str: "04.07"
}

puts "🧪 Тестирование простого парсера цен"
puts "=" * 50
puts "Маршрут: #{test_route[:from]}-#{test_route[:to]} на #{test_route[:date_str]}"
puts

price = parser.get_price(test_route)

if price
  puts "✅ Цена найдена: #{price} руб."
else
  puts "❌ Цена не найдена"
end

puts "\n📁 Проверьте файлы в папке logs/ для отладки HTML"
