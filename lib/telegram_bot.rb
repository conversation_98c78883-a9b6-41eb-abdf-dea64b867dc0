require 'telegram/bot'
require 'yaml'

class TelegramNotifier
  def initialize(config_path = 'config/config.yml')
    @config_path = config_path
    @config = YAML.load_file(config_path)
    @bot_token = @config['telegram']['bot_token']
    @chat_ids = @config['telegram']['chat_ids']
    @running = false
  end

  def send_price_report(price_data)
    return false if @bot_token == "YOUR_BOT_TOKEN_HERE" || @chat_ids.empty?

    message = format_price_message(price_data)
    
    begin
      Telegram::Bot::Client.run(@bot_token) do |bot|
        @chat_ids.each do |chat_id|
          bot.api.send_message(
            chat_id: chat_id,
            text: message,
            parse_mode: 'Markdown'
          )
          puts "Сообщение отправлено в чат #{chat_id}"
        end
      end
      true
    rescue => e
      puts "Ошибка при отправке в Telegram: #{e.message}"
      false
    end
  end

  def add_subscriber(chat_id)
    return false if @chat_ids.include?(chat_id)
    
    @chat_ids << chat_id
    save_config
    true
  end

  def remove_subscriber(chat_id)
    return false unless @chat_ids.include?(chat_id)

    @chat_ids.delete(chat_id)
    save_config
    true
  end

  def stop_bot
    puts "🛑 Остановка Telegram бота..."
    @running = false
  end

  def start_bot
    return false if @bot_token == "YOUR_BOT_TOKEN_HERE"

    puts "🤖 Запуск Telegram бота..."
    puts "Токен: #{@bot_token[0..10]}..."
    puts "Для остановки нажмите Ctrl+C"

    @running = true

    begin
      Telegram::Bot::Client.run(@bot_token) do |bot|
        bot.listen do |message|
          handle_message(bot, message) if @running
        end
      end
    rescue Interrupt
      puts "\n🛑 Остановка бота..."
      @running = false
    rescue => e
      puts "❌ Ошибка бота: #{e.message}"
      false
    end
  end

  def send_price_report(price_data)
    return false if @bot_token == "YOUR_BOT_TOKEN_HERE" || @chat_ids.empty?

    message = format_price_message(price_data)

    begin
      Telegram::Bot::Client.run(@bot_token) do |bot|
        @chat_ids.each do |chat_id|
          bot.api.send_message(
            chat_id: chat_id,
            text: message,
            parse_mode: 'Markdown'
          )
          puts "Сообщение отправлено в чат #{chat_id}"
        end
      end
      true
    rescue => e
      puts "Ошибка при отправке в Telegram: #{e.message}"
      false
    end
  end

  private

  def format_price_message(price_data)
    message = "🛫 *Цены на авиабилеты*\n\n"
    
    if price_data.empty?
      message += "❌ Не удалось получить данные о ценах"
      return message
    end

    # Группируем по маршрутам
    routes = price_data.group_by { |item| "#{item[:from]}-#{item[:to]}" }
    
    routes.each do |route, items|
      message += "📍 *#{route}*\n"
      
      items.each do |item|
        price_text = item[:price] ? "#{item[:price]} руб." : "Цена не найдена"
        message += "• #{item[:date_str]}: #{price_text}\n"
      end
      
      message += "\n"
    end
    
    message += "_Обновлено: #{Time.now.strftime('%d.%m.%Y %H:%M')}_"
    message
  end

  private

  def handle_message(bot, message)
    return unless message.is_a?(Telegram::Bot::Types::Message)

    chat_id = message.chat.id
    text = message.text
    user_name = message.from.first_name || "Пользователь"

    puts "📨 Сообщение от #{user_name} (#{chat_id}): #{text}"

    case text
    when '/start'
      send_welcome_message(bot, chat_id, user_name)
    when '/help'
      send_help_message(bot, chat_id)
    when '/subscribe'
      subscribe_user(bot, chat_id, user_name)
    when '/unsubscribe'
      unsubscribe_user(bot, chat_id, user_name)
    when '/status'
      send_status_message(bot, chat_id)
    when '/prices'
      send_current_prices(bot, chat_id)
    else
      send_unknown_command(bot, chat_id)
    end
  rescue => e
    puts "❌ Ошибка обработки сообщения: #{e.message}"
  end

  def send_welcome_message(bot, chat_id, user_name)
    message = "👋 Привет, #{user_name}!\n\n"
    message += "🛫 Я бот для мониторинга цен на авиабилеты.\n\n"
    message += "📋 *Доступные команды:*\n"
    message += "/subscribe - Подписаться на уведомления\n"
    message += "/unsubscribe - Отписаться от уведомлений\n"
    message += "/status - Статус подписки\n"
    message += "/prices - Текущие цены\n"
    message += "/help - Помощь\n\n"
    message += "✈️ Мониторим маршруты: VVO-HKT, VVO-BKK"

    bot.api.send_message(
      chat_id: chat_id,
      text: message,
      parse_mode: 'Markdown'
    )
  end

  def send_help_message(bot, chat_id)
    message = "🆘 *Помощь*\n\n"
    message += "📋 *Команды:*\n"
    message += "• `/start` - Начать работу с ботом\n"
    message += "• `/subscribe` - Подписаться на уведомления о ценах\n"
    message += "• `/unsubscribe` - Отписаться от уведомлений\n"
    message += "• `/status` - Проверить статус подписки\n"
    message += "• `/prices` - Получить текущие цены\n"
    message += "• `/help` - Показать эту справку\n\n"
    message += "🛫 *О сервисе:*\n"
    message += "Бот автоматически проверяет цены на авиабилеты и отправляет уведомления подписчикам.\n\n"
    message += "📍 *Маршруты:*\n"
    message += "• VVO ↔ HKT (Владивосток - Пхукет)\n"
    message += "• VVO ↔ BKK (Владивосток - Бангкок)\n\n"
    message += "⏰ Проверка цен происходит автоматически каждые несколько часов."

    bot.api.send_message(
      chat_id: chat_id,
      text: message,
      parse_mode: 'Markdown'
    )
  end

  def subscribe_user(bot, chat_id, user_name)
    if @chat_ids.include?(chat_id)
      message = "✅ #{user_name}, вы уже подписаны на уведомления!"
    else
      @chat_ids << chat_id
      save_config
      message = "🎉 #{user_name}, вы успешно подписались на уведомления о ценах на авиабилеты!\n\n"
      message += "📬 Теперь вы будете получать актуальную информацию о ценах на маршруты:\n"
      message += "• VVO → HKT\n"
      message += "• VVO → BKK\n\n"
      message += "🔕 Чтобы отписаться, используйте команду /unsubscribe"

      puts "✅ Новый подписчик: #{user_name} (#{chat_id})"
    end

    bot.api.send_message(
      chat_id: chat_id,
      text: message,
      parse_mode: 'Markdown'
    )
  end

  def unsubscribe_user(bot, chat_id, user_name)
    if @chat_ids.include?(chat_id)
      @chat_ids.delete(chat_id)
      save_config
      message = "😢 #{user_name}, вы отписались от уведомлений.\n\n"
      message += "Чтобы снова подписаться, используйте команду /subscribe"

      puts "❌ Пользователь отписался: #{user_name} (#{chat_id})"
    else
      message = "❓ #{user_name}, вы не были подписаны на уведомления.\n\n"
      message += "Чтобы подписаться, используйте команду /subscribe"
    end

    bot.api.send_message(
      chat_id: chat_id,
      text: message,
      parse_mode: 'Markdown'
    )
  end

  def send_status_message(bot, chat_id)
    is_subscribed = @chat_ids.include?(chat_id)
    total_subscribers = @chat_ids.length

    message = "📊 *Статус подписки*\n\n"

    if is_subscribed
      message += "✅ Вы подписаны на уведомления\n"
    else
      message += "❌ Вы не подписаны на уведомления\n"
    end

    message += "👥 Всего подписчиков: #{total_subscribers}\n\n"

    if is_subscribed
      message += "🔕 Чтобы отписаться: /unsubscribe"
    else
      message += "🔔 Чтобы подписаться: /subscribe"
    end

    bot.api.send_message(
      chat_id: chat_id,
      text: message,
      parse_mode: 'Markdown'
    )
  end

  def send_current_prices(bot, chat_id)
    message = "⏳ Получаю актуальные цены...\n"
    message += "Это может занять несколько секунд."

    # Отправляем сообщение о загрузке
    loading_message = bot.api.send_message(
      chat_id: chat_id,
      text: message
    )

    # Получаем цены (упрощенная версия)
    begin
      require_relative 'route_manager'
      require_relative 'price_parser'

      route_manager = RouteManager.new(@config_path)
      price_parser = PriceParser.new(@config_path)

      routes = route_manager.get_valid_routes # Берем все маршруты
      price_data = []

      routes.each do |route|
        price = price_parser.get_price(route)
        price_data << {
          from: route[:from],
          to: route[:to],
          date_str: route[:date_str],
          price: price
        }
      end

      # Удаляем сообщение о загрузке
      bot.api.delete_message(
        chat_id: chat_id,
        message_id: loading_message.message_id
      )

      # Отправляем результат
      result_message = format_price_message(price_data)
      bot.api.send_message(
        chat_id: chat_id,
        text: result_message,
        parse_mode: 'Markdown'
      )

    rescue => e
      # Удаляем сообщение о загрузке
      bot.api.delete_message(
        chat_id: chat_id,
        message_id: loading_message.message_id
      ) rescue nil

      error_message = "❌ Ошибка получения цен: #{e.message}\n\n"
      error_message += "Попробуйте позже или обратитесь к администратору."

      bot.api.send_message(
        chat_id: chat_id,
        text: error_message
      )
    end
  end

  def send_unknown_command(bot, chat_id)
    message = "❓ Неизвестная команда.\n\n"
    message += "Используйте /help для просмотра доступных команд."

    bot.api.send_message(
      chat_id: chat_id,
      text: message
    )
  end

  def save_config
    @config['telegram']['chat_ids'] = @chat_ids
    File.write(@config_path, @config.to_yaml)
  end
end
