require 'json'
require 'fileutils'
require 'time'
require 'yaml'

class PriceCache
  def initialize(config_path = 'config/config.yml')
    @config = YAML.load_file(config_path)
    @cache_config = @config['cache']
    @enabled = @cache_config['enabled']
    @ttl_minutes = @cache_config['ttl_minutes']
    @cache_file = @cache_config['file_path']
    
    # Создаем директорию для кеша если её нет
    FileUtils.mkdir_p(File.dirname(@cache_file)) if @enabled
    
    @cache_data = load_cache
  end

  def get_price(route_key)
    return nil unless @enabled
    
    cached_item = @cache_data[route_key]
    return nil unless cached_item
    
    # Проверяем актуальность кеша
    cached_time = Time.parse(cached_item['timestamp'])
    if Time.now - cached_time > (@ttl_minutes * 60)
      # Кеш устарел, удаляем запись
      @cache_data.delete(route_key)
      save_cache
      return nil
    end
    
    puts "🗄️  Цена найдена в кеше: #{cached_item['price']} руб. (кеш от #{cached_time.strftime('%H:%M:%S')})"
    cached_item['price']
  end

  def set_price(route_key, price)
    return unless @enabled && price
    
    @cache_data[route_key] = {
      'price' => price,
      'timestamp' => Time.now.iso8601
    }
    
    save_cache
    puts "💾 Цена сохранена в кеш: #{price} руб."
  end

  def clear_cache
    return unless @enabled
    
    @cache_data = {}
    save_cache
    puts "🗑️  Кеш очищен"
  end

  def get_cache_info
    return "Кеширование отключено" unless @enabled
    
    if @cache_data.empty?
      return "Кеш пуст"
    end
    
    info = "📊 Информация о кеше:\n"
    info += "⏰ TTL: #{@ttl_minutes} минут\n"
    info += "📦 Записей в кеше: #{@cache_data.size}\n\n"
    
    @cache_data.each do |route_key, data|
      cached_time = Time.parse(data['timestamp'])
      age_minutes = ((Time.now - cached_time) / 60).round
      status = age_minutes < @ttl_minutes ? "✅ актуальна" : "❌ устарела"
      
      info += "• #{route_key}: #{data['price']} руб. (#{age_minutes} мин. назад) #{status}\n"
    end
    
    info
  end

  def cleanup_expired
    return unless @enabled
    
    expired_keys = []
    @cache_data.each do |route_key, data|
      cached_time = Time.parse(data['timestamp'])
      if Time.now - cached_time > (@ttl_minutes * 60)
        expired_keys << route_key
      end
    end
    
    expired_keys.each { |key| @cache_data.delete(key) }
    
    if expired_keys.any?
      save_cache
      puts "🧹 Удалено #{expired_keys.size} устаревших записей из кеша"
    end
  end

  private

  def load_cache
    return {} unless @enabled
    return {} unless File.exist?(@cache_file)
    
    begin
      JSON.parse(File.read(@cache_file))
    rescue JSON::ParserError, Errno::ENOENT
      puts "⚠️  Ошибка чтения кеша, создаем новый"
      {}
    end
  end

  def save_cache
    return unless @enabled
    
    begin
      File.write(@cache_file, JSON.pretty_generate(@cache_data))
    rescue => e
      puts "❌ Ошибка сохранения кеша: #{e.message}"
    end
  end

  def generate_route_key(route_info)
    "#{route_info[:from]}_#{route_info[:to]}_#{route_info[:date_str]}"
  end
end
