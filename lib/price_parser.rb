require 'httparty'
require 'yaml'
require 'json'
require 'digest'

class Price<PERSON>ars<PERSON>
  def initialize(config_path = 'config/config.yml')
    @config = YAML.load_file(config_path)
    @timeout = @config['parsing']['timeout']
    @retry_attempts = @config['parsing']['retry_attempts']
    @api_url = "https://www.bgoperator.ru/site?action=biletgraphql&task=biletjson"
  end

  def get_price(route_info)
    attempts = 0

    begin
      attempts += 1
      puts "Попытка #{attempts}: Запрос к API для #{route_info[:from]}-#{route_info[:to]} на #{route_info[:date_str]}"

      # Формируем поисковую строку с правильным хешем
      search_string = build_search_string_with_hash(route_info)

      # Создаем GraphQL запрос
      graphql_query = build_graphql_query(search_string)

      response = HTTParty.post(@api_url, {
        timeout: @timeout,
        headers: {
          'Content-Type' => 'application/json',
          'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept' => 'application/json',
          'X-Requested-With' => 'XMLHttpRequest',
          'Referer' => 'https://www.bgoperator.ru/price.shtml'
        },
        body: graphql_query.to_json
      })

      puts "HTTP статус: #{response.code}"
      puts "Размер ответа: #{response.body.length} символов"

      if response.code == 200
        price = parse_price_from_json(response.body)
        if price
          puts "✅ Найдена цена: #{price} руб."
          return price
        else
          puts "Цена не найдена в ответе API"
          save_debug_json(response.body, route_info) if @config['debug']
        end
      else
        puts "HTTP ошибка: #{response.code}"
        puts "Ответ сервера: #{response.body[0..500]}" if response.body
      end

    rescue => e
      puts "Ошибка при запросе к API: #{e.message}"

      if attempts < @retry_attempts
        puts "Повторная попытка через 5 секунд..."
        sleep(5)
        retry
      else
        puts "Превышено количество попыток"
      end
    end

    nil
  end

  private

  def get_session_hash(route_info)
    begin
      # Загружаем страницу поиска, чтобы получить правильный хеш
      date_formatted = "0" + route_info[:date].strftime('%d%m%Y')
      search_url = "https://www.bgoperator.ru/price.shtml?action=biletnew#/air/search/#{date_formatted}_#{route_info[:from]}_#{route_info[:to]}./1000"

      puts "Получаем хеш сессии с: #{search_url}"

      response = HTTParty.get(search_url, {
        timeout: @timeout,
        headers: {
          'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      })

      if response.code == 200
        # Ищем хеш в JavaScript коде страницы
        hash_match = response.body.match(/searchflights['"]:['"]([^'"]+)['"]/)
        if hash_match
          search_string = hash_match[1]
          # Извлекаем хеш из строки поиска
          hash_match = search_string.match(/\.\/([a-f0-9]{32})$/)
          if hash_match
            hash = hash_match[1]
            puts "Найден хеш сессии: #{hash}"
            return hash
          end
        end
      end

      puts "Не удалось получить хеш сессии"
      nil
    rescue => e
      puts "Ошибка при получении хеша сессии: #{e.message}"
      nil
    end
  end

  def build_search_string_with_hash(route_info, hash = nil)
    # Формат: "004072025_VVO_BKK./44d156b693ed5cd23ecda4d3634da62d"
    date_formatted = "0" + route_info[:date].strftime('%d%m%Y')
    base_string = "#{date_formatted}_#{route_info[:from]}_#{route_info[:to]}"

    # Генерируем правильный хеш: MD5 от (e + e.substring(4, 10))
    # где e = "003072025_VVO_BKK." (с точкой в конце)
    e = base_string + "."
    hash_input = e + e[4, 6]  # e + e.substring(4, 10) - берем 6 символов начиная с позиции 4
    calculated_hash = Digest::MD5.hexdigest(hash_input)

    search_string = "#{base_string}./#{calculated_hash}"
    puts "Базовая строка (e): #{e}"
    puts "Подстрока e[4,6]: #{e[4, 6]}"
    puts "Строка для хеша: #{hash_input}"
    puts "Вычисленный хеш: #{calculated_hash}"
    puts "Поисковая строка: #{search_string}"

    search_string
  end

  def build_graphql_query(search_string)
    {
      "query" => "query Index {meta {...F0}} fragment F0 on Meta {__searchdata2It0H4:_searchdata(searchflights:\"#{search_string}\",adt:1,chd:0,inf:0,airline:\"\",direct:\"0\",rand:0) {id,airlines {id,n},locations {id,n},fares {id,h,p,bd,c,f},nodes {id,ac,acid,acn,ad,an,at,c,f,d,dc,dcid,dcn,dd,dn,dt,t,oa},routes {id,n},groups {id,n}}}",
      "variables" => {}
    }
  end

  def parse_price_from_json(json_string)
    begin
      data = JSON.parse(json_string)
      
      # Ищем данные о ценах в структуре ответа
      search_data = data.dig('data', 'meta', '__searchdata2It0H4')
      
      if search_data && search_data['fares'] && !search_data['fares'].empty?
        # Берем первую цену из массива fares
        first_fare = search_data['fares'].first
        price = first_fare['p'].to_i if first_fare['p']
        
        if price && price > 0
          puts "Найдена цена через API: #{price} руб."
          return price
        end
      end
      
      # Альтернативный поиск в других полях
      if search_data && search_data['groups']
        search_data['groups'].each do |group|
          if group['p'] && group['p'].to_i > 0
            price = group['p'].to_i
            puts "Найдена цена в группах: #{price} руб."
            return price
          end
        end
      end
      
      puts "Структура ответа не содержит ожидаемых данных о ценах"
      return nil
      
    rescue JSON::ParserError => e
      puts "Ошибка парсинга JSON: #{e.message}"
      return nil
    end
  end

  def save_debug_json(json_string, route_info)
    return unless @config['debug']

    filename = "api_response_#{route_info[:from]}_#{route_info[:to]}_#{route_info[:date_str].gsub('.', '')}.json"
    filepath = File.join('logs', filename)

    File.write(filepath, JSON.pretty_generate(JSON.parse(json_string)))
    puts "JSON ответ сохранен для отладки: #{filepath}"
  rescue
    # Если не удается распарсить JSON, сохраняем как есть
    File.write(filepath, json_string)
    puts "Сырой ответ сохранен для отладки: #{filepath}"
  end
end
