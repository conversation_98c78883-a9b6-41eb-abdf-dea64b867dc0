require 'date'
require 'yaml'

class RouteManager
  def initialize(config_path = 'config/config.yml')
    @config = YAML.load_file(config_path)
    @routes = @config['routes']
  end

  def get_valid_routes
    valid_routes = []
    current_date = Date.today

    @routes.each do |route_name, route_data|
      route_data['dates'].each do |date_str|
        # Преобразуем дату в формат для сравнения (добавляем текущий год)
        day, month = date_str.split('.')
        year = current_date.year
        
        # Если дата уже прошла в этом году, берем следующий год
        route_date = Date.new(year, month.to_i, day.to_i)
        if route_date < current_date
          route_date = Date.new(year + 1, month.to_i, day.to_i)
        end

        # Пропускаем даты, которые уже прошли
        next if route_date < current_date

        valid_routes << {
          name: route_name,
          from: route_data['from'],
          to: route_data['to'],
          date: route_date,
          date_str: date_str
        }
      end
    end

    valid_routes
  end
end
