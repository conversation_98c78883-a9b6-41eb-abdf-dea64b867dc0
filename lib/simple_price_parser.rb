require 'httparty'
require 'nokogiri'
require 'yaml'

class <PERSON><PERSON><PERSON><PERSON>ars<PERSON>
  def initialize(config_path = 'config/config.yml')
    @config = YAML.load_file(config_path)
    @timeout = @config['parsing']['timeout']
    @retry_attempts = @config['parsing']['retry_attempts']
  end

  def get_price(route_info)
    attempts = 0
    
    begin
      attempts += 1
      puts "Попытка #{attempts}: Загружаем страницу для #{route_info[:from]}-#{route_info[:to]} на #{route_info[:date_str]}"
      
      # Формируем URL для прямого доступа к результатам
      url = build_search_url(route_info)
      puts "URL: #{url}"
      
      response = HTTParty.get(url, {
        timeout: @timeout,
        headers: {
          'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language' => 'ru-RU,ru;q=0.8,en-US;q=0.5,en;q=0.3',
          'Connection' => 'keep-alive'
        }
      })

      puts "HTTP статус: #{response.code}"
      puts "Размер ответа: #{response.body.length} символов"

      if response.code == 200
        price = parse_price_from_html(response.body, route_info)
        return price if price
        
        puts "Цена не найдена на странице"
        save_debug_html(response.body, route_info) if @config['debug']
        return nil
      else
        puts "HTTP ошибка: #{response.code}"
        return nil
      end

    rescue => e
      puts "Ошибка при загрузке страницы: #{e.message}"
      
      if attempts < @retry_attempts
        puts "Повторная попытка через 5 секунд..."
        sleep(5)
        retry
      else
        puts "Превышено количество попыток"
        return nil
      end
    end
  end

  private

  def build_search_url(route_info)
    # Попробуем разные варианты URL
    date_formatted = "0" + route_info[:date].strftime('%d%m%Y')
    
    # Вариант 1: Прямая ссылка на поиск
    base_url = "https://www.bgoperator.ru/price.shtml"
    params = {
      'action' => 'biletnew',
      'from' => route_info[:from],
      'to' => route_info[:to],
      'date' => date_formatted,
      'adt' => '1',
      'chd' => '0',
      'inf' => '0'
    }
    
    query_string = params.map { |k, v| "#{k}=#{v}" }.join('&')
    "#{base_url}?#{query_string}"
  end

  def parse_price_from_html(html, route_info)
    doc = Nokogiri::HTML(html)
    
    # Множественные селекторы для поиска цены
    price_selectors = [
      '.index_main-price_3mv',
      '.price',
      '.main-price',
      '[class*="price"]',
      '[class*="cost"]',
      '.fare-price',
      '.ticket-price'
    ]
    
    price_selectors.each do |selector|
      elements = doc.css(selector)
      elements.each do |element|
        price_text = element.text.strip
        price_number = price_text.gsub(/[^\d]/, '').to_i
        
        if price_number > 1000  # Минимальная разумная цена
          puts "Найдена цена с селектором '#{selector}': #{price_number} руб."
          return price_number
        end
      end
    end
    
    # Поиск цены в тексте различными паттернами
    price_patterns = [
      /(\d{4,6})\s*руб\./,
      /(\d{4,6})\s*рублей/,
      /(\d{4,6})\s*₽/,
      /"price"[^}]*?(\d{4,6})/,
      /"cost"[^}]*?(\d{4,6})/,
      /price[^}]*?(\d{4,6})/i,
      /от\s+(\d{4,6})\s+руб/i,
      /цена[^0-9]*(\d{4,6})/i
    ]
    
    price_patterns.each_with_index do |pattern, index|
      matches = html.scan(pattern)
      matches.each do |match|
        price = match[0].to_i
        if price > 1000  # Минимальная разумная цена
          puts "Найдена цена паттерном #{index + 1}: #{price} руб."
          return price
        end
      end
    end
    
    # Проверяем, есть ли сообщения об ошибках
    if html.include?('Рейсов не найдено') || html.include?('Нет доступных рейсов')
      puts "На выбранную дату рейсов не найдено"
    elsif html.include?('Ошибка') || html.include?('Error')
      puts "Ошибка на странице поиска"
    end
    
    nil
  end

  def save_debug_html(html, route_info)
    return unless @config['debug']
    
    filename = "simple_search_#{route_info[:from]}_#{route_info[:to]}_#{route_info[:date_str].gsub('.', '')}.html"
    filepath = File.join('logs', filename)
    
    File.write(filepath, html)
    puts "HTML сохранен для отладки: #{filepath}"
  end
end
