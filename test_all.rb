#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'
require_relative 'lib/telegram_bot'
require 'yaml'

class SystemTester
  def initialize
    @route_manager = RouteManager.new
    @price_parser = PriceParser.new
    @telegram_notifier = TelegramNotifier.new
  end

  def run_all_tests
    puts "🧪 Запуск полного тестирования системы"
    puts "=" * 60
    
    test_route_manager
    test_price_parser
    test_telegram_config
    
    puts "\n" + "=" * 60
    puts "✅ Все тесты завершены!"
  end

  private

  def test_route_manager
    puts "\n📋 Тестирование RouteManager..."
    
    routes = @route_manager.get_valid_routes
    puts "Найдено маршрутов: #{routes.length}"
    
    if routes.empty?
      puts "⚠️  Нет актуальных маршрутов (возможно, все даты в прошлом)"
    else
      puts "✅ RouteManager работает корректно"
      routes.each do |route|
        puts "  • #{route[:from]}-#{route[:to]} на #{route[:date_str]}"
      end
    end
  end

  def test_price_parser
    puts "\n💰 Тестирование PriceParser..."
    
    # Тестируем с одним маршрутом
    test_route = {
      from: "VVO",
      to: "HKT",
      date: Date.new(2025, 7, 4),
      date_str: "04.07"
    }
    
    puts "Тестируем маршрут: #{test_route[:from]}-#{test_route[:to]} на #{test_route[:date_str]}"
    
    price = @price_parser.get_price(test_route)
    
    if price
      puts "✅ PriceParser работает корректно"
      puts "  Найдена цена: #{price} руб."
    else
      puts "❌ PriceParser не смог получить цену"
    end
  end

  def test_telegram_config
    puts "\n📱 Проверка конфигурации Telegram..."
    
    config = YAML.load_file('config/config.yml')
    bot_token = config['telegram']['bot_token']
    chat_ids = config['telegram']['chat_ids']
    
    if bot_token == "YOUR_BOT_TOKEN_HERE"
      puts "⚠️  Токен Telegram бота не настроен"
      puts "   Добавьте реальный токен в config/config.yml"
    else
      puts "✅ Токен Telegram бота настроен"
    end
    
    if chat_ids.empty?
      puts "⚠️  Нет подписчиков для уведомлений"
      puts "   Добавьте Chat ID в config/config.yml или используйте manage_subscribers.rb"
    else
      puts "✅ Настроены подписчики: #{chat_ids.length} чат(ов)"
    end
    
    if bot_token != "YOUR_BOT_TOKEN_HERE" && !chat_ids.empty?
      puts "\n📤 Тестирование отправки сообщения..."
      
      test_data = [{
        from: "VVO",
        to: "TEST",
        date_str: "TEST",
        price: 12345
      }]
      
      if @telegram_notifier.send_price_report(test_data)
        puts "✅ Тестовое сообщение отправлено успешно"
      else
        puts "❌ Ошибка отправки тестового сообщения"
      end
    end
  end
end

if __FILE__ == $0
  tester = SystemTester.new
  tester.run_all_tests
end
