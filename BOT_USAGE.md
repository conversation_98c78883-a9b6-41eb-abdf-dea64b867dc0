# 🤖 Использование Telegram бота

## Запуск бота

### Вариант 1: Только бот
```bash
ruby bot.rb
```

### Вариант 2: Бот + автоматический мониторинг (рекомендуется)
```bash
ruby run_service.rb
# Выберите пункт 4
```

## Команды бота

### `/start`
Начать работу с ботом. Показывает приветственное сообщение и список доступных команд.

**Пример:**
```
👋 Привет, Александр!

🛫 Я бот для мониторинга цен на авиабилеты.

📋 Доступные команды:
/subscribe - Подписаться на уведомления
/unsubscribe - Отписаться от уведомлений
/status - Статус подписки
/prices - Текущие цены
/help - Помощь

✈️ Мониторим маршруты: VVO-HKT, VVO-BKK, VVO-MLE, VVO-SEZ
```

### `/subscribe`
Подписаться на автоматические уведомления о ценах на авиабилеты.

**Результат:**
- Пользователь добавляется в список подписчиков
- Будет получать уведомления при каждой проверке цен
- Конфигурация автоматически сохраняется

### `/unsubscribe`
Отписаться от уведомлений.

**Результат:**
- Пользователь удаляется из списка подписчиков
- Перестает получать автоматические уведомления

### `/status`
Проверить статус подписки и общую статистику.

**Пример ответа:**
```
📊 Статус подписки

✅ Вы подписаны на уведомления
👥 Всего подписчиков: 5

🔕 Чтобы отписаться: /unsubscribe
```

### `/prices`
Получить актуальные цены прямо сейчас (не дожидаясь автоматической проверки).

**Особенности:**
- Выполняет реальный запрос к API
- Может занять несколько секунд
- Показывает цены для первых 2 маршрутов (для быстроты)

### `/help`
Показать справку по всем командам и информацию о сервисе.

### `/cache`
Показать информацию о кеше цен.

**Пример ответа:**
```
📊 Информация о кеше:
⏰ TTL: 15 минут
📦 Записей в кеше: 3

• VVO_HKT_27.06: 26339 руб. (5 мин. назад) ✅ актуальна
• VVO_BKK_27.06: 26339 руб. (12 мин. назад) ✅ актуальна
```

### `/menu`
Показать главное меню с интерактивными кнопками.

## Интерактивное меню

Бот теперь поддерживает удобное интерактивное меню с кнопками:

- **📊 Текущие цены** - получить актуальные цены
- **📈 Статус** - проверить статус подписки
- **🔔 Подписаться** - подписаться на уведомления
- **🔕 Отписаться** - отписаться от уведомлений
- **🗄️ Кеш** - информация о кеше и управление им
- **❓ Помощь** - справка по командам

### Управление кешем
В меню кеша доступны дополнительные действия:
- **🗑️ Очистить кеш** - принудительно очистить весь кеш
- **🔙 Главное меню** - вернуться к главному меню

## Кеширование

Система теперь использует кеширование для ускорения работы:

### Особенности кеша:
- **TTL**: 15 минут (настраивается в config.yml)
- **Автоматическая очистка**: устаревшие записи удаляются автоматически
- **Быстрый доступ**: повторные запросы цен выполняются мгновенно
- **Прозрачность**: пользователь видит, когда данные взяты из кеша

### Преимущества:
- Снижение нагрузки на API bgoperator.ru
- Быстрые ответы на команду `/prices`
- Экономия времени при частых запросах

## Автоматические уведомления

Когда запущен мониторинг (режим 4 в `run_service.rb`), все подписчики автоматически получают сообщения с актуальными ценами.

**Пример уведомления:**
```
🛫 Цены на авиабилеты

📍 VVO-HKT
• 27.06: 26339 руб.
• 01.07: 26339 руб.
• 04.07: 26339 руб.

📍 VVO-BKK
• 27.06: 26339 руб.
• 30.06: 26339 руб.
• 04.07: 26339 руб.

Обновлено: 24.06.2025 16:55
```

## Управление подписчиками

### Автоматическое управление
- Пользователи сами подписываются через команды бота
- Не требует ручного добавления Chat ID
- Конфигурация обновляется автоматически

### Ручное управление (опционально)
Можно использовать скрипт `manage_subscribers.rb` для:
- Просмотра списка подписчиков
- Ручного добавления/удаления
- Отправки тестовых сообщений

## Безопасность

### Приватность
- Бот сохраняет только Chat ID пользователей
- Никакая личная информация не собирается
- Пользователи могут отписаться в любой момент

### Ограничения
- Бот отвечает только на зарегистрированные команды
- Неизвестные команды игнорируются с подсказкой

## Мониторинг работы бота

### Логи в консоли
```
📨 Сообщение от Александр (123456789): /subscribe
✅ Новый подписчик: Александр (123456789)
```

### Статистика
- Количество подписчиков отображается в команде `/status`
- Логи показывают активность пользователей

## Решение проблем

### Бот не отвечает
1. Проверьте токен в `config/config.yml`
2. Убедитесь, что бот запущен (`ruby bot.rb`)
3. Проверьте интернет-соединение

### Ошибки в консоли
- Все ошибки логируются в консоль
- При критических ошибках бот продолжает работать
- Перезапустите бота при необходимости

### Команды не работают
- Убедитесь, что команды начинаются с `/`
- Используйте `/help` для списка команд
- Проверьте, что бот добавлен в чат (для групп)

## Развертывание в продакшене

### Автозапуск
```bash
# Linux/macOS (systemd)
sudo systemctl enable bg-ticker-bot

# Windows (Task Scheduler)
# Создайте задание для запуска ruby run_service.rb
```

### Мониторинг
- Используйте `screen` или `tmux` для фонового запуска
- Настройте логирование в файл
- Мониторьте использование ресурсов
