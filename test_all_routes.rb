#!/usr/bin/env ruby

require_relative 'lib/telegram_bot'

# Тестируем команду /prices с всеми маршрутами
class MockBot
  def initialize
    @api = MockAPI.new
  end
  
  attr_reader :api
end

class <PERSON>ckAP<PERSON>
  def send_message(options)
    puts "📤 Отправка сообщения:"
    puts "  Chat ID: #{options[:chat_id]}"
    puts "  Text: #{options[:text]}"
    puts
    
    # Возвращаем объект с message_id
    MockMessage.new(12345)
  end
  
  def delete_message(options)
    puts "🗑️  Удаление сообщения ID: #{options[:message_id]}"
    puts
  end
end

class MockMessage
  def initialize(id)
    @message_id = id
  end
  
  attr_reader :message_id
end

# Тестируем
puts "🧪 Тестирование команды /prices со всеми маршрутами"
puts "=" * 60

notifier = TelegramNotifier.new
bot = MockBot.new
chat_id = 123456789

# Вызываем приватный метод через send
notifier.send(:send_current_prices, bot, chat_id)

puts "✅ Тест завершен"
