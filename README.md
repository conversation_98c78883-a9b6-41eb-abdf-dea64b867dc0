# Мониторинг цен на авиабилеты

Консольное Ruby приложение для мониторинга цен на авиабилеты через сайт bgoperator.ru с отправкой уведомлений в Telegram.

## ✅ Функциональность

- ✅ Проверка цен для маршрутов VVO-HKT, VVO-BKK, VVO-MLE, VVO-SEZ
- ✅ Автоматическая фильтрация прошедших дат
- ✅ Использование GraphQL API для получения актуальных цен
- ✅ Правильная генерация хешей для API запросов
- ✅ Отправка результатов через Telegram бот
- ✅ Настраиваемые маршруты и даты
- ✅ Обработка ошибок и повторные попытки

## 🚀 Установка

1. Установите зависимости:
```bash
bundle install
```

2. Настройте конфигурацию в `config/config.yml`:
   - Добавьте токен Telegram бота
   - Добавьте ID чатов для уведомлений

## 📱 Telegram бот

### Настройка:
Подробные инструкции см. в файле `TELEGRAM_SETUP.md`

1. Создайте бота через @BotFather в Telegram
2. Получите токен бота
3. Добавьте токен в `config/config.yml`
4. Запустите бота: `ruby bot.rb`

### Команды бота:
- `/start` - Начать работу с ботом
- `/subscribe` - Подписаться на уведомления
- `/unsubscribe` - Отписаться от уведомлений
- `/status` - Статус подписки
- `/prices` - Получить текущие цены
- `/cache` - Информация о кеше
- `/menu` - Интерактивное меню
- `/help` - Справка по командам

### Новые возможности:
- **🎛️ Интерактивное меню** с inline-кнопками для удобного управления
- **🗄️ Кеширование цен** на 15 минут для быстрого доступа
- **⚡ Быстрые ответы** при повторных запросах цен
- **🧹 Управление кешем** через команды бота

### Автоматическая подписка:
Пользователи могут самостоятельно подписываться на уведомления через команды бота, без необходимости ручного добавления Chat ID.

## 🏃 Запуск

### Интерактивный сервис (рекомендуется):
```bash
ruby run_service.rb
```

### Только Telegram бот:
```bash
ruby bot.rb
```

### Разовая проверка цен:
```bash
ruby main.rb
```

### Управление подписчиками:
```bash
ruby manage_subscribers.rb
```

### Тестирование:
```bash
ruby test_parser.rb
ruby test_all.rb
```

## 📊 Пример результата

```
🛫 Запуск мониторинга цен на авиабилеты...
==================================================
📋 Найдено 6 актуальных маршрутов:
  • VVO-HKT на 27.06
  • VVO-HKT на 01.07
  • VVO-HKT на 04.07
  • VVO-BKK на 27.06
  • VVO-BKK на 30.06
  • VVO-BKK на 04.07

==================================================
📊 РЕЗУЛЬТАТЫ:
==================================================

🛫 VVO-HKT:
------------------------------
  ✅ 27.06    | 26339 руб.
  ✅ 01.07    | 26339 руб.
  ✅ 04.07    | 26339 руб.

🛫 VVO-BKK:
------------------------------
  ✅ 27.06    | 26339 руб.
  ✅ 30.06    | 26339 руб.
  ✅ 04.07    | 26339 руб.

📱 Отправка результатов в Telegram...
✅ Результаты успешно отправлены
```

## 🏗️ Структура проекта

- `main.rb` - главный файл приложения
- `lib/route_manager.rb` - управление маршрутами и датами
- `lib/price_parser.rb` - парсинг цен через GraphQL API
- `lib/telegram_bot.rb` - отправка уведомлений в Telegram
- `config/config.yml` - конфигурация приложения
- `manage_subscribers.rb` - управление подписчиками Telegram

## ⚙️ Настройка маршрутов

Маршруты настраиваются в файле `config/config.yml`:

```yaml
routes:
  vvo_hkt:
    from: "VVO"
    to: "HKT"
    dates: ["27.06", "01.07", "04.07"]
  vvo_bkk:
    from: "VVO"
    to: "BKK"
    dates: ["27.06", "30.06", "04.07"]
  vvo_mle:
    from: "VVO"
    to: "MLE"
    dates: ["24.06", "25.06", "26.06", "27.06", "28.06"]
  vvo_sez:
    from: "VVO"
    to: "SEZ"
    dates: ["24.06", "25.06", "26.06", "27.06", "28.06"]
```

## 🔧 Техническая информация

Приложение использует GraphQL API сайта bgoperator.ru:
- URL: `https://www.bgoperator.ru/site?action=biletgraphql&task=biletjson`
- Генерация хешей: MD5 от строки `e + e.substring(4, 10)`, где `e = "003072025_VVO_BKK."`
- Формат поисковой строки: `"003072025_VVO_BKK./44d156b693ed5cd23ecda4d3634da62d"`

## 📝 Примечания

- Приложение автоматически пропускает даты, которые уже прошли
- Между запросами есть пауза в 3 секунды для избежания блокировки
- При ошибках загрузки выполняется до 3 попыток
- Результаты отображаются в консоли и отправляются в Telegram
- Для отладки можно включить сохранение JSON ответов в `config.yml`
