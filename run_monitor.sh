#!/bin/bash

# Скрипт для автоматического запуска мониторинга цен
# Можно добавить в cron для регулярного выполнения

# Переходим в директорию проекта
cd "$(dirname "$0")"

# Логируем время запуска
echo "$(date): Запуск мониторинга цен на билеты" >> logs/monitor.log

# Запускаем приложение и логируем результат
ruby main.rb >> logs/monitor.log 2>&1

# Логируем завершение
echo "$(date): Мониторинг завершен" >> logs/monitor.log
echo "----------------------------------------" >> logs/monitor.log
