#!/usr/bin/env ruby

require_relative 'lib/telegram_bot'

# Тестируем доступность метода stop_bot
notifier = TelegramNotifier.new

puts "🧪 Тестирование метода stop_bot"
puts "Доступные методы: #{notifier.public_methods(false)}"

if notifier.respond_to?(:stop_bot)
  puts "✅ Метод stop_bot доступен"
  notifier.stop_bot
  puts "✅ Метод stop_bot выполнен успешно"
else
  puts "❌ Метод stop_bot недоступен"
end
