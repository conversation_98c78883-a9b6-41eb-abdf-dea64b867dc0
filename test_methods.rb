#!/usr/bin/env ruby

require_relative 'lib/telegram_bot'

# Тестируем доступность методов
notifier = TelegramNotifier.new

puts "🧪 Тестирование публичных методов"
puts "Доступные методы: #{notifier.public_methods(false).sort}"

methods_to_test = [:start_bot, :stop_bot, :send_price_report, :add_subscriber, :remove_subscriber]

methods_to_test.each do |method|
  if notifier.respond_to?(method)
    puts "✅ #{method} - доступен"
  else
    puts "❌ #{method} - недоступен"
  end
end
