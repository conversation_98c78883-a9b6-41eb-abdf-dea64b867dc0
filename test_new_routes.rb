#!/usr/bin/env ruby

require_relative 'lib/route_manager'

puts "🧪 Тестирование новых маршрутов"
puts "=" * 50

route_manager = RouteManager.new
routes = route_manager.get_valid_routes

puts "📋 Найдено маршрутов: #{routes.length}"
puts

# Группируем маршруты по направлениям
routes_by_destination = routes.group_by { |r| "#{r[:from]}-#{r[:to]}" }

routes_by_destination.each do |destination, route_list|
  destination_name = case destination
  when "VVO-HKT"
    "Владивосток → Пхукет"
  when "VVO-BKK"
    "Владивосток → Бангкок"
  when "VVO-MLE"
    "Владивосток → Мальдивы"
  when "VVO-SEZ"
    "Владивосток → Сейшелы"
  else
    destination
  end
  
  puts "✈️ #{destination_name}:"
  route_list.each do |route|
    puts "   • #{route[:date_str]} (#{route[:date].strftime('%A')})"
  end
  puts
end

puts "=" * 50
puts "✅ Конфигурация маршрутов корректна!"

# Проверяем, что новые маршруты добавлены
mle_routes = routes.select { |r| r[:to] == "MLE" }
sez_routes = routes.select { |r| r[:to] == "SEZ" }

puts "\n📊 Статистика:"
puts "🏝️ Маршруты на Мальдивы (MLE): #{mle_routes.length}"
puts "🏖️ Маршруты на Сейшелы (SEZ): #{sez_routes.length}"

if mle_routes.length > 0 && sez_routes.length > 0
  puts "\n🎉 Новые маршруты успешно добавлены!"
else
  puts "\n❌ Ошибка: новые маршруты не найдены"
end
