#!/usr/bin/env ruby

require_relative 'lib/telegram_bot'

class TelegramBotRunner
  def initialize
    @notifier = TelegramNotifier.new
  end

  def run
    puts "🤖 Запуск интерактивного Telegram бота"
    puts "=" * 50
    
    # Проверяем конфигурацию
    config = YAML.load_file('config/config.yml')
    bot_token = config['telegram']['bot_token']
    
    if bot_token == "YOUR_BOT_TOKEN_HERE"
      puts "❌ Ошибка: Токен Telegram бота не настроен!"
      puts ""
      puts "Для настройки:"
      puts "1. Создайте бота через @BotFather в Telegram"
      puts "2. Получите токен бота"
      puts "3. Добавьте токен в config/config.yml"
      puts ""
      puts "Подробные инструкции в файле TELEGRAM_SETUP.md"
      return false
    end
    
    puts "✅ Токен настроен: #{bot_token[0..10]}..."
    puts ""
    puts "🚀 Запуск бота..."
    puts "📱 Пользователи могут найти бота и использовать команды:"
    puts "   /start - Начать работу"
    puts "   /subscribe - Подписаться на уведомления"
    puts "   /help - Помощь"
    puts ""
    puts "🛑 Для остановки нажмите Ctrl+C"
    puts ""
    
    @notifier.start_bot
  end
end

if __FILE__ == $0
  runner = TelegramBotRunner.new
  runner.run
end
