#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'
require_relative 'lib/telegram_bot'

class TicketPriceMonitor
  def initialize
    @route_manager = RouteManager.new
    @price_parser = PriceParser.new
    @telegram_notifier = TelegramNotifier.new
  end

  def run
    puts "🛫 Запуск мониторинга цен на авиабилеты..."
    puts "=" * 50
    
    routes = @route_manager.get_valid_routes
    
    if routes.empty?
      puts "❌ Нет актуальных маршрутов для проверки"
      return
    end

    puts "📋 Найдено #{routes.length} актуальных маршрутов:"
    routes.each do |route|
      puts "  • #{route[:from]}-#{route[:to]} на #{route[:date_str]}"
    end
    puts

    price_data = []

    routes.each_with_index do |route, index|
      puts "[#{index + 1}/#{routes.length}] Проверяем #{route[:from]}-#{route[:to]} на #{route[:date_str]}..."
      
      price = @price_parser.get_price(route)
      
      price_data << {
        from: route[:from],
        to: route[:to],
        date_str: route[:date_str],
        price: price
      }

      # Небольшая пауза между запросами
      sleep(3) unless index == routes.length - 1
    end

    puts "\n" + "=" * 50
    puts "📊 РЕЗУЛЬТАТЫ:"
    puts "=" * 50

    display_results_table(price_data)
    
    # Отправляем результаты в Telegram
    puts "\n📱 Отправка результатов в Telegram..."
    if @telegram_notifier.send_price_report(price_data)
      puts "✅ Результаты успешно отправлены"
    else
      puts "⚠️  Не удалось отправить в Telegram (проверьте настройки)"
    end
  end

  private

  def display_results_table(price_data)
    # Группируем по маршрутам
    routes = price_data.group_by { |item| "#{item[:from]}-#{item[:to]}" }
    
    routes.each do |route, items|
      puts "\n🛫 #{route}:"
      puts "-" * 30
      
      items.each do |item|
        price_text = item[:price] ? "#{item[:price]} руб." : "Цена не найдена"
        status = item[:price] ? "✅" : "❌"
        puts "  #{status} #{item[:date_str].ljust(8)} | #{price_text}"
      end
    end
  end
end

# Запуск приложения
if __FILE__ == $0
  monitor = TicketPriceMonitor.new
  monitor.run
end
