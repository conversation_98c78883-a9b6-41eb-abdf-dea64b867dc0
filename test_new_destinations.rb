#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'

puts "🧪 Тестирование цен для новых направлений"
puts "=" * 60

route_manager = RouteManager.new
price_parser = PriceParser.new

# Получаем маршруты для новых направлений
routes = route_manager.get_valid_routes
mle_routes = routes.select { |r| r[:to] == "MLE" }.first(2) # Берем первые 2 для теста
sez_routes = routes.select { |r| r[:to] == "SEZ" }.first(2) # Берем первые 2 для теста

test_routes = mle_routes + sez_routes

if test_routes.empty?
  puts "❌ Нет доступных маршрутов для тестирования"
  exit 1
end

puts "🎯 Тестируем #{test_routes.length} маршрутов:"
test_routes.each do |route|
  destination_name = case route[:to]
  when "MLE"
    "Мальдивы"
  when "SEZ"
    "Сейшелы"
  end
  puts "  • #{route[:from]}-#{route[:to]} на #{route[:date_str]} (#{destination_name})"
end

puts "\n" + "=" * 60

test_routes.each_with_index do |route, index|
  destination_name = case route[:to]
  when "MLE"
    "Мальдивы"
  when "SEZ"
    "Сейшелы"
  end
  
  puts "\n[#{index + 1}/#{test_routes.length}] 🧪 Тестируем #{route[:from]}-#{route[:to]} на #{route[:date_str]} (#{destination_name})"
  puts "⏰ Время начала: #{Time.now.strftime('%H:%M:%S')}"
  
  begin
    start_time = Time.now
    price = price_parser.get_price(route)
    end_time = Time.now
    
    duration = (end_time - start_time).round(2)
    
    if price
      puts "✅ Цена найдена: #{price} руб."
      puts "⏱️  Время выполнения: #{duration} сек"
    else
      puts "❌ Цена не найдена"
      puts "⏱️  Время выполнения: #{duration} сек"
      puts "ℹ️  Возможно, рейсы на это направление недоступны"
    end
    
  rescue => e
    puts "❌ Ошибка: #{e.message}"
  end
  
  # Пауза между запросами
  if index < test_routes.length - 1
    puts "⏳ Пауза 5 секунд..."
    sleep(5)
  end
end

puts "\n" + "=" * 60
puts "📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:"

# Показываем информацию о кеше
puts "\n🗄️ Информация о кеше:"
puts price_parser.get_cache_info

puts "\n✅ Тестирование новых направлений завершено!"
puts "📝 Примечание: Если цены не найдены, возможно bgoperator.ru"
puts "   не предлагает рейсы на эти направления в указанные даты."
