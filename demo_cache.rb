#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'

puts "🎬 ДЕМОНСТРАЦИЯ КЕШИРОВАНИЯ"
puts "=" * 50

# Получаем первый доступный маршрут
route_manager = RouteManager.new
routes = route_manager.get_valid_routes

if routes.empty?
  puts "❌ Нет доступных маршрутов"
  exit 1
end

test_route = routes.first
puts "📍 Тестируем маршрут: #{test_route[:from]}-#{test_route[:to]} на #{test_route[:date_str]}"

# Создаем парсер с кешем
parser = PriceParser.new

puts "\n1️⃣ ПЕРВЫЙ ЗАПРОС (загрузка с сайта):"
puts "⏰ Время начала: #{Time.now.strftime('%H:%M:%S')}"

start_time = Time.now
price1 = parser.get_price(test_route)
end_time = Time.now

duration1 = (end_time - start_time).round(2)
puts "⏱️  Время выполнения: #{duration1} секунд"
puts "💰 Цена: #{price1 ? "#{price1} руб." : "не найдена"}"

puts "\n" + "-" * 30

puts "\n2️⃣ ВТОРОЙ ЗАПРОС (из кеша):"
puts "⏰ Время начала: #{Time.now.strftime('%H:%M:%S')}"

start_time = Time.now
price2 = parser.get_price(test_route)
end_time = Time.now

duration2 = (end_time - start_time).round(2)
puts "⏱️  Время выполнения: #{duration2} секунд"
puts "💰 Цена: #{price2 ? "#{price2} руб." : "не найдена"}"

puts "\n" + "-" * 30

puts "\n📊 СРАВНЕНИЕ:"
puts "🐌 Первый запрос (с сайта): #{duration1} сек"
puts "⚡ Второй запрос (из кеша): #{duration2} сек"

if duration1 > 0 && duration2 > 0
  speedup = (duration1 / duration2).round(1)
  puts "🚀 Ускорение в #{speedup} раз!"
end

puts "\n📋 ИНФОРМАЦИЯ О КЕШЕ:"
puts parser.get_cache_info

puts "\n" + "=" * 50
puts "✅ Демонстрация завершена!"
puts "🗄️ Кеш работает и ускоряет повторные запросы"
