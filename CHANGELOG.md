# История изменений

## v1.2.0 - 24.06.2025

### 🆕 Новые функции:
- **🗄️ Кеширование цен** - цены кешируются на 15 минут для быстрого доступа
- **🎛️ Интерактивное меню** с inline-кнопками для удобного управления ботом
- **⚡ Быстрые ответы** - повторные запросы цен выполняются мгновенно из кеша
- **🧹 Управление кешем** - просмотр информации и очистка кеша через бот

### 📋 Новые команды бота:
- `/cache` - Информация о кеше цен
- `/menu` - Показать интерактивное меню с кнопками

### 🎛️ Интерактивные кнопки:
- **📊 Текущие цены** - получить актуальные цены (с кешированием)
- **📈 Статус** - проверить статус подписки
- **🔔 Подписаться** / **🔕 Отписаться** - управление подпиской
- **🗄️ Кеш** - информация о кеше и управление им
- **❓ Помощь** - справка по командам

### 🔧 Технические улучшения:
- **Автоматическая очистка** устаревших записей кеша
- **Прозрачность кеширования** - пользователь видит источник данных
- **Настраиваемый TTL** кеша через config.yml
- **Обратная совместимость** - все старые команды работают

### 📁 Новые файлы:
- `lib/price_cache.rb` - класс для работы с кешем
- `cache/price_cache.json` - файл с кешированными данными
- `test_cache.rb` - тестирование системы кеширования
- `CACHE_AND_MENU_GUIDE.md` - руководство по новым возможностям

## v1.1.1 - 24.06.2025

### ✅ Исправления:
- **Команда `/prices` теперь показывает все маршруты** - Убрано ограничение на 2 маршрута, теперь показываются все 6 маршрутов
- **Улучшена информативность** - Добавлен счетчик маршрутов и примерное время выполнения

## v1.1.0 - 24.06.2025

### ✅ Исправления:
- **Исправлена ошибка в команде `/prices`** - Устранена ошибка "Missing attribute: result" при удалении сообщений в Telegram боте
- **Улучшена обработка ошибок** в `run_service.rb` при остановке сервиса
- **Исправлены методы доступа** - `start_bot` и `stop_bot` перенесены в публичную секцию класса

### 🆕 Новые функции:
- **Интерактивный Telegram бот** с поддержкой команд и polling режима
- **Автоматическая подписка** через команды бота (`/subscribe`, `/unsubscribe`)
- **Команда `/prices`** для получения актуальных цен по запросу
- **Интерактивный сервис** `run_service.rb` с меню выбора режимов работы
- **Комбинированный режим** - бот + мониторинг одновременно

### 🔧 Технические улучшения:
- **Правильный алгоритм хеширования** для GraphQL API
- **Улучшенная обработка ошибок** во всех компонентах
- **Автоматическое управление подписчиками** через конфигурационный файл
- **Подробное логирование** всех операций

## v1.0.0 - 24.06.2025

### 🎉 Первый релиз:
- **Базовый мониторинг цен** через GraphQL API bgoperator.ru
- **Telegram уведомления** для подписчиков
- **Настраиваемые маршруты** VVO-HKT и VVO-BKK
- **Автоматическая фильтрация дат** (пропуск прошедших)
- **Обработка ошибок** с повторными попытками

### 📋 Команды Telegram бота:
- `/start` - Приветствие и инструкции
- `/subscribe` - Подписка на уведомления
- `/unsubscribe` - Отписка от уведомлений
- `/status` - Статус подписки
- `/prices` - Получить текущие цены
- `/help` - Справка по командам

### 🛠️ Структура проекта:
```
bg_ticker_scan/
├── main.rb              # Разовая проверка цен
├── bot.rb               # Запуск только бота
├── run_service.rb       # Интерактивный сервис
├── lib/
│   ├── route_manager.rb # Управление маршрутами
│   ├── price_parser.rb  # GraphQL API парсер
│   └── telegram_bot.rb  # Telegram бот с polling
├── config/config.yml    # Конфигурация
└── документация...
```

### 📊 Результаты тестирования:
```
✅ CONFIG: Конфигурация корректна
✅ DEPENDENCIES: Все зависимости установлены
✅ PRICE PARSER: Парсер работает, найдена цена: 26339 руб.
✅ TELEGRAM CONFIG: Токен настроен, подписчиков: 1
✅ FILE STRUCTURE: Все файлы на месте

🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! СИСТЕМА ГОТОВА К РАБОТЕ!
```

### 🚀 Способы запуска:
- `ruby main.rb` - Разовая проверка цен
- `ruby bot.rb` - Только Telegram бот
- `ruby run_service.rb` - Интерактивный режим с меню
- `ruby final_test.rb` - Полное тестирование системы
