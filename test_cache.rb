#!/usr/bin/env ruby

require_relative 'lib/price_cache'
require_relative 'lib/route_manager'
require 'yaml'

puts "🧪 Тестирование системы кеширования"
puts "=" * 50

# Создаем тестовые данные
route_manager = RouteManager.new
routes = route_manager.get_valid_routes

if routes.empty?
  puts "❌ Нет доступных маршрутов для тестирования"
  exit 1
end

test_route = routes.first
puts "📍 Тестовый маршрут: #{test_route[:from]}-#{test_route[:to]} на #{test_route[:date_str]}"

# Инициализируем кеш
cache = PriceCache.new

puts "\n1️⃣ Тестирование пустого кеша..."
route_key = "#{test_route[:from]}_#{test_route[:to]}_#{test_route[:date_str]}"
cached_price = cache.get_price(route_key)

if cached_price.nil?
  puts "✅ Кеш пуст - корректно"
else
  puts "❌ Кеш должен быть пуст"
end

puts "\n2️⃣ Добавление цены в кеш..."
test_price = 25000
cache.set_price(route_key, test_price)

puts "\n3️⃣ Проверка получения цены из кеша..."
cached_price = cache.get_price(route_key)

if cached_price == test_price
  puts "✅ Цена корректно получена из кеша: #{cached_price} руб."
else
  puts "❌ Ошибка получения цены из кеша"
end

puts "\n4️⃣ Информация о кеше..."
puts cache.get_cache_info

puts "\n5️⃣ Тестирование очистки кеша..."
cache.clear_cache

cached_price = cache.get_price(route_key)
if cached_price.nil?
  puts "✅ Кеш успешно очищен"
else
  puts "❌ Ошибка очистки кеша"
end

puts "\n6️⃣ Тестирование интеграции с PriceParser..."
begin
  require_relative 'lib/price_parser'
  
  parser = PriceParser.new
  
  puts "Информация о кеше через PriceParser:"
  puts parser.get_cache_info
  
  puts "✅ Интеграция с PriceParser работает"
rescue => e
  puts "❌ Ошибка интеграции с PriceParser: #{e.message}"
end

puts "\n" + "=" * 50
puts "🎉 Тестирование кеширования завершено!"
puts "📁 Файл кеша: cache/price_cache.json"

# Показываем содержимое файла кеша если он существует
cache_file = 'cache/price_cache.json'
if File.exist?(cache_file)
  puts "\n📄 Содержимое файла кеша:"
  puts File.read(cache_file)
end
