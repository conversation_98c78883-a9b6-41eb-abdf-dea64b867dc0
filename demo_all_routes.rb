#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'

puts "🌍 ДЕМОНСТРАЦИЯ ВСЕХ МАРШРУТОВ"
puts "=" * 60

route_manager = RouteManager.new
price_parser = PriceParser.new

routes = route_manager.get_valid_routes

puts "📊 Общая статистика:"
puts "🛫 Всего маршрутов: #{routes.length}"

# Группируем по направлениям
routes_by_destination = routes.group_by { |r| r[:to] }

destinations = {
  'HKT' => '🏝️ Пхукет, Таиланд',
  'BKK' => '🏙️ Бангкок, Таиланд', 
  'MLE' => '🏝️ Мальдивы',
  'SEZ' => '🏖️ Сейшелы'
}

routes_by_destination.each do |dest_code, dest_routes|
  dest_name = destinations[dest_code] || dest_code
  puts "#{dest_name}: #{dest_routes.length} маршрутов"
end

puts "\n" + "=" * 60
puts "🧪 ТЕСТИРОВАНИЕ ОДНОГО МАРШРУТА ИЗ КАЖДОГО НАПРАВЛЕНИЯ"
puts "=" * 60

# Берем по одному маршруту из каждого направления
test_routes = []
routes_by_destination.each do |dest_code, dest_routes|
  test_routes << dest_routes.first
end

test_routes.each_with_index do |route, index|
  dest_name = destinations[route[:to]] || route[:to]
  
  puts "\n[#{index + 1}/#{test_routes.length}] 🧪 #{route[:from]} → #{route[:to]} на #{route[:date_str]}"
  puts "📍 Направление: #{dest_name}"
  puts "⏰ Время: #{Time.now.strftime('%H:%M:%S')}"
  
  begin
    start_time = Time.now
    price = price_parser.get_price(route)
    end_time = Time.now
    
    duration = (end_time - start_time).round(2)
    
    if price
      puts "✅ Цена: #{price.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse} руб."
      puts "⏱️  Время: #{duration} сек"
      
      # Определяем источник данных
      if duration < 0.1
        puts "🗄️  Источник: кеш"
      else
        puts "🌐 Источник: API bgoperator.ru"
      end
    else
      puts "❌ Цена не найдена"
      puts "⏱️  Время: #{duration} сек"
      puts "ℹ️  Возможно, нет рейсов на эту дату"
    end
    
  rescue => e
    puts "❌ Ошибка: #{e.message}"
  end
  
  # Пауза между запросами (кроме последнего)
  if index < test_routes.length - 1
    puts "⏳ Пауза 3 секунды..."
    sleep(3)
  end
end

puts "\n" + "=" * 60
puts "📋 ИНФОРМАЦИЯ О КЕШЕ"
puts "=" * 60

puts price_parser.get_cache_info

puts "\n" + "=" * 60
puts "🎯 РЕКОМЕНДАЦИИ ПО НАПРАВЛЕНИЯМ"
puts "=" * 60

puts "✅ РЕКОМЕНДУЕМЫЕ:"
puts "  🏝️ Пхукет (HKT) - стабильно работает"
puts "  🏙️ Бангкок (BKK) - стабильно работает"
puts "  🏝️ Мальдивы (MLE) - работает, но не все даты"

puts "\n⚠️  ТРЕБУЕТ ПРОВЕРКИ:"
puts "  🏖️ Сейшелы (SEZ) - возможно, нет прямых рейсов"

puts "\n💡 СОВЕТЫ:"
puts "  • Используйте кеширование для быстрых повторных запросов"
puts "  • Проверяйте актуальность рейсов на сайте bgoperator.ru"
puts "  • Мониторинг работает автоматически каждые 2 часа"

puts "\n🚀 ЗАПУСК ПОЛНОГО МОНИТОРИНГА:"
puts "  ruby run_service.rb → выберите пункт 4"

puts "\n" + "=" * 60
puts "✅ Демонстрация завершена!"
