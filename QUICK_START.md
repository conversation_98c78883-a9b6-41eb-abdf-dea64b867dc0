# 🚀 Быстрый старт

## 1. Установка

```bash
# Клонируйте или скачайте проект
cd bg_ticker_scan

# Установите зависимости
bundle install
```

## 2. Первый запуск

### Интерактивный режим (рекомендуется):
```bash
ruby run_service.rb
```

### Только проверка цен:
```bash
ruby main.rb
```

Вы увидите результат примерно такой:
```
🛫 Запуск мониторинга цен на авиабилеты...
==================================================
📋 Найдено 6 актуальных маршрутов:
  • VVO-HKT на 27.06
  • VVO-HKT на 01.07
  • VVO-HKT на 04.07
  • VVO-BKK на 27.06
  • VVO-BKK на 30.06
  • VVO-BKK на 04.07

==================================================
📊 РЕЗУЛЬТАТЫ:
==================================================

🛫 VVO-HKT:
------------------------------
  ✅ 27.06    | 26339 руб.
  ✅ 01.07    | 26339 руб.
  ✅ 04.07    | 26339 руб.

🛫 VVO-BKK:
------------------------------
  ✅ 27.06    | 26339 руб.
  ✅ 30.06    | 26339 руб.
  ✅ 04.07    | 26339 руб.

📱 Отправка результатов в Telegram...
⚠️  Не удалось отправить в Telegram (проверьте настройки)
```

## 3. Настройка Telegram (опционально)

### Создание бота:
1. Найдите @BotFather в Telegram
2. Отправьте `/newbot`
3. Следуйте инструкциям
4. Сохраните токен

### Получение Chat ID:
1. Отправьте сообщение вашему боту
2. Откройте: `https://api.telegram.org/bot<TOKEN>/getUpdates`
3. Найдите `"chat":{"id":123456789}`

### Настройка:
Отредактируйте `config/config.yml`:
```yaml
telegram:
  bot_token: "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
  chat_ids: []  # Будет заполняться автоматически при подписке через бота
```

### Запуск бота:
```bash
# Только бот
ruby bot.rb

# Бот + мониторинг
ruby run_service.rb
# Выберите пункт 4
```

### Использование бота:
1. Найдите вашего бота в Telegram
2. Отправьте `/start`
3. Используйте `/subscribe` для подписки
4. Получайте автоматические уведомления о ценах!

## 4. Управление подписчиками

```bash
ruby manage_subscribers.rb
```

Выберите:
- `1` - Показать подписчиков
- `2` - Добавить подписчика
- `3` - Удалить подписчика
- `4` - Отправить тестовое сообщение

## 5. Тестирование

```bash
# Полное тестирование системы
ruby test_all.rb

# Тестирование только парсера
ruby test_parser.rb
```

## 6. Автоматический запуск

### Windows (Task Scheduler):
1. Откройте "Планировщик заданий"
2. Создайте задание
3. Укажите: `ruby C:\path\to\main.rb`
4. Настройте расписание

### Linux/macOS (cron):
```bash
# Редактировать crontab
crontab -e

# Добавить строку (каждые 2 часа)
0 */2 * * * cd /path/to/project && ruby main.rb
```

## 7. Настройка маршрутов

Отредактируйте `config/config.yml`:
```yaml
routes:
  vvo_hkt:
    from: "VVO"
    to: "HKT"
    dates: ["27.06", "01.07", "04.07"]
  vvo_bkk:
    from: "VVO" 
    to: "BKK"
    dates: ["27.06", "30.06", "04.07"]
  # Добавьте свои маршруты
  new_route:
    from: "SVO"
    to: "LED"
    dates: ["15.07", "20.07"]
```

## 🔧 Решение проблем

### Ошибка "No such file or directory"
```bash
# Убедитесь, что находитесь в правильной папке
pwd
ls -la

# Проверьте наличие файлов
ls config/config.yml
ls lib/
```

### Ошибка "bundle: command not found"
```bash
# Установите bundler
gem install bundler
```

### Цены не найдены
- Проверьте интернет-соединение
- Возможно, сайт временно недоступен
- Попробуйте позже

### Telegram не работает
- Проверьте токен бота
- Убедитесь, что Chat ID правильный
- Попробуйте отправить тестовое сообщение

## 📞 Поддержка

Если возникли проблемы:
1. Проверьте логи в папке `logs/`
2. Запустите `ruby test_all.rb`
3. Включите отладку в `config/config.yml` (`debug: true`)
