#!/usr/bin/env ruby

require 'digest'

# Тестируем алгоритм генерации хеша
def test_hash_algorithm
  # Пример из задания: 004072025_VVO_BKK./44d156b693ed5cd23ecda4d3634da62d
  # Строка для хеша: e + e.substring(4, 10), где e = "003072025_VVO_BKK."
  e = "003072025_VVO_BKK."
  expected_hash = "44d156b693ed5cd23ecda4d3634da62d"

  puts "Тестирование алгоритма генерации хеша"
  puts "=" * 50
  puts "Базовая строка (e): #{e}"
  puts "e.length: #{e.length}"
  puts "e[4, 6]: #{e[4, 6]}"
  puts "Ожидаемый хеш: #{expected_hash}"
  puts

  # Правильный алгоритм: e + e.substring(4, 10)
  hash_input = e + e[4, 6]
  calculated_hash = Digest::MD5.hexdigest(hash_input)

  puts "Строка для хеша: #{hash_input}"
  puts "Вычисленный хеш: #{calculated_hash}"
  puts "Совпадает: #{calculated_hash == expected_hash ? '✅' : '❌'}"

  if calculated_hash == expected_hash
    puts "\n✅ Алгоритм найден!"
    return hash_input
  else
    puts "\n❌ Хеш не совпадает"
    return nil
  end
end

# Тестируем с разными примерами
def test_different_routes
  puts "\n" + "=" * 50
  puts "Тестирование разных маршрутов"
  puts "=" * 50
  
  test_cases = [
    "004072025_VVO_BKK",
    "004072025_VVO_HKT", 
    "027062025_VVO_HKT",
    "001072025_VVO_HKT"
  ]
  
  test_cases.each do |e|
    hash_input = e + e[4, 6]
    calculated_hash = Digest::MD5.hexdigest(hash_input)
    puts "#{e} -> #{calculated_hash}"
  end
end

test_hash_algorithm
test_different_routes
