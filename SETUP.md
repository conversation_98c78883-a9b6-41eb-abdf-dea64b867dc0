# Инструкция по настройке

## 1. Установка зависимостей

Убедитесь, что у вас установлен Ruby (версия 2.7 или выше).

Установите зависимости:
```bash
bundle install
```

## 2. Настройка Telegram бота

### Создание бота:
1. Откройте Telegram и найдите @BotFather
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Сохраните полученный токен

### Получение Chat ID:
1. Добавьте вашего бота в чат или группу
2. Отправьте любое сообщение боту
3. Откройте в браузере: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. Найдите в ответе поле "chat" -> "id" - это ваш Chat ID

### Настройка конфигурации:
Отредактируйте файл `config/config.yml`:
```yaml
telegram:
  bot_token: "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"  # Ваш токен
  chat_ids: [123456789, -987654321]  # Ваши Chat ID
```

## 3. Тестирование

### Проверка конфигурации:
```bash
ruby manage_subscribers.rb
```

### Отправка тестового сообщения:
В меню управления подписчиками выберите пункт 4.

### Запуск мониторинга:
```bash
ruby main.rb
```

## 4. Автоматический запуск

### Для Linux/macOS (cron):
```bash
# Редактировать crontab
crontab -e

# Добавить строку для запуска каждые 2 часа
0 */2 * * * /path/to/your/project/run_monitor.sh
```

### Для Windows (Task Scheduler):
1. Откройте "Планировщик заданий"
2. Создайте новое задание
3. Укажите путь к `run_monitor.sh` или `ruby main.rb`
4. Настройте расписание

## 5. Мониторинг логов

Логи сохраняются в файл `logs/monitor.log`:
```bash
tail -f logs/monitor.log
```

## Примечания

- Приложение автоматически пропускает прошедшие даты
- Рекомендуется запускать не чаще чем раз в час
- При ошибках проверьте логи и настройки сети
