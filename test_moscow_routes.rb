#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'

puts "🧪 Тестирование маршрутов из Москвы"
puts "=" * 60

route_manager = RouteManager.new
price_parser = PriceParser.new

# Получаем маршруты из Москвы
routes = route_manager.get_valid_routes
moscow_routes = routes.select { |r| r[:from] == "SVO" }

if moscow_routes.empty?
  puts "❌ Нет маршрутов из Москвы (SVO)"
  exit 1
end

puts "📊 Найдено маршрутов из Москвы: #{moscow_routes.length}"
puts

# Группируем по направлениям
routes_by_destination = moscow_routes.group_by { |r| r[:to] }

routes_by_destination.each do |dest_code, dest_routes|
  destination_name = case dest_code
  when "MLE"
    "🏝️ Мальдивы"
  when "SEZ"
    "🏖️ Сейшелы"
  else
    dest_code
  end
  
  puts "#{destination_name}:"
  dest_routes.each do |route|
    puts "   • #{route[:date_str]} (#{route[:date].strftime('%A')})"
  end
  puts
end

puts "=" * 60
puts "🧪 ТЕСТИРОВАНИЕ ЦЕНЫ ДЛЯ КАЖДОГО НАПРАВЛЕНИЯ"
puts "=" * 60

# Берем по одному маршруту из каждого направления
test_routes = []
routes_by_destination.each do |dest_code, dest_routes|
  # Берем первый доступный маршрут
  test_routes << dest_routes.first
end

test_routes.each_with_index do |route, index|
  destination_name = case route[:to]
  when "MLE"
    "🏝️ Мальдивы"
  when "SEZ"
    "🏖️ Сейшелы"
  end
  
  puts "\n[#{index + 1}/#{test_routes.length}] 🧪 Москва → #{destination_name}"
  puts "📍 Маршрут: #{route[:from]}-#{route[:to]} на #{route[:date_str]}"
  puts "⏰ Время начала: #{Time.now.strftime('%H:%M:%S')}"
  
  begin
    start_time = Time.now
    price = price_parser.get_price(route)
    end_time = Time.now
    
    duration = (end_time - start_time).round(2)
    
    if price
      formatted_price = price.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse
      puts "✅ Цена найдена: #{formatted_price} руб."
      puts "⏱️  Время выполнения: #{duration} сек"
      
      if duration < 0.1
        puts "🗄️  Источник: кеш"
      else
        puts "🌐 Источник: API bgoperator.ru"
      end
    else
      puts "❌ Цена не найдена"
      puts "⏱️  Время выполнения: #{duration} сек"
      puts "ℹ️  Возможные причины:"
      puts "   • Нет рейсов на эту дату"
      puts "   • Направление недоступно из Москвы"
      puts "   • Временная недоступность на сайте"
    end
    
  rescue => e
    puts "❌ Ошибка: #{e.message}"
  end
  
  # Пауза между запросами
  if index < test_routes.length - 1
    puts "⏳ Пауза 5 секунд..."
    sleep(5)
  end
end

puts "\n" + "=" * 60
puts "📋 ИНФОРМАЦИЯ О КЕШЕ"
puts "=" * 60

puts price_parser.get_cache_info

puts "\n" + "=" * 60
puts "📊 СВОДКА ПО МОСКОВСКИМ МАРШРУТАМ"
puts "=" * 60

puts "🛫 Отправление: SVO (Москва, Шереметьево)"
puts "🎯 Направления:"

routes_by_destination.each do |dest_code, dest_routes|
  destination_name = case dest_code
  when "MLE"
    "🏝️ Мальдивы (Мале)"
  when "SEZ"
    "🏖️ Сейшелы (Маэ)"
  end
  
  puts "  #{destination_name}: #{dest_routes.length} маршрутов"
end

puts "\n💡 РЕКОМЕНДАЦИИ:"
puts "  • Московские маршруты могут иметь больше вариантов рейсов"
puts "  • Проверяйте актуальность на сайте bgoperator.ru"
puts "  • Используйте кеширование для быстрых повторных запросов"

puts "\n✅ Тестирование московских маршрутов завершено!"
