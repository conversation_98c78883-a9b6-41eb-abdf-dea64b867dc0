#!/usr/bin/env ruby

require 'yaml'

class FinalSystemTest
  def initialize
    @config = YAML.load_file('config/config.yml')
  end

  def run_all_tests
    puts "🧪 ФИНАЛЬНОЕ ТЕСТИРОВАНИЕ СИСТЕМЫ"
    puts "=" * 60
    puts "⏰ #{Time.now.strftime('%d.%m.%Y %H:%M:%S')}"
    puts

    results = {
      config: test_configuration,
      dependencies: test_dependencies,
      price_parser: test_price_parser,
      telegram_config: test_telegram_configuration,
      file_structure: test_file_structure
    }

    puts "\n" + "=" * 60
    puts "📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:"
    puts "=" * 60

    results.each do |test_name, result|
      status = result[:success] ? "✅" : "❌"
      puts "#{status} #{test_name.to_s.upcase.gsub('_', ' ')}: #{result[:message]}"
    end

    all_passed = results.values.all? { |r| r[:success] }
    
    puts "\n" + "=" * 60
    if all_passed
      puts "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! СИСТЕМА ГОТОВА К РАБОТЕ!"
      puts ""
      puts "🚀 Для запуска используйте:"
      puts "   ruby run_service.rb  # Интерактивный режим"
      puts "   ruby bot.rb          # Только Telegram бот"
      puts "   ruby main.rb         # Разовая проверка цен"
    else
      puts "⚠️  ОБНАРУЖЕНЫ ПРОБЛЕМЫ. ПРОВЕРЬТЕ НАСТРОЙКИ."
      puts ""
      puts "📖 Инструкции по настройке:"
      puts "   README.md           # Общая документация"
      puts "   QUICK_START.md      # Быстрый старт"
      puts "   TELEGRAM_SETUP.md   # Настройка Telegram"
    end
    puts "=" * 60
  end

  private

  def test_configuration
    begin
      required_keys = ['telegram', 'routes', 'parsing']
      missing_keys = required_keys - @config.keys
      
      if missing_keys.empty?
        { success: true, message: "Конфигурация корректна" }
      else
        { success: false, message: "Отсутствуют ключи: #{missing_keys.join(', ')}" }
      end
    rescue => e
      { success: false, message: "Ошибка чтения конфигурации: #{e.message}" }
    end
  end

  def test_dependencies
    begin
      require 'httparty'
      require 'telegram/bot'
      require 'nokogiri'
      require 'digest'
      require 'json'
      
      { success: true, message: "Все зависимости установлены" }
    rescue LoadError => e
      { success: false, message: "Отсутствует зависимость: #{e.message}" }
    end
  end

  def test_price_parser
    begin
      require_relative 'lib/price_parser'
      require_relative 'lib/route_manager'
      
      route_manager = RouteManager.new
      price_parser = PriceParser.new
      
      routes = route_manager.get_valid_routes
      if routes.empty?
        return { success: false, message: "Нет актуальных маршрутов" }
      end

      # Тестируем один маршрут
      test_route = routes.first
      price = price_parser.get_price(test_route)
      
      if price && price > 0
        { success: true, message: "Парсер работает, найдена цена: #{price} руб." }
      else
        { success: false, message: "Парсер не смог получить цену" }
      end
    rescue => e
      { success: false, message: "Ошибка парсера: #{e.message}" }
    end
  end

  def test_telegram_configuration
    bot_token = @config['telegram']['bot_token']
    chat_ids = @config['telegram']['chat_ids']
    
    if bot_token == "YOUR_BOT_TOKEN_HERE"
      { success: false, message: "Токен Telegram не настроен" }
    elsif bot_token.nil? || bot_token.empty?
      { success: false, message: "Токен Telegram отсутствует" }
    else
      message = "Токен настроен"
      message += ", подписчиков: #{chat_ids.length}" if chat_ids.is_a?(Array)
      { success: true, message: message }
    end
  end

  def test_file_structure
    required_files = [
      'main.rb',
      'bot.rb', 
      'run_service.rb',
      'lib/route_manager.rb',
      'lib/price_parser.rb',
      'lib/telegram_bot.rb',
      'config/config.yml',
      'Gemfile'
    ]
    
    missing_files = required_files.reject { |file| File.exist?(file) }
    
    if missing_files.empty?
      { success: true, message: "Все файлы на месте" }
    else
      { success: false, message: "Отсутствуют файлы: #{missing_files.join(', ')}" }
    end
  end
end

if __FILE__ == $0
  tester = FinalSystemTest.new
  tester.run_all_tests
end
