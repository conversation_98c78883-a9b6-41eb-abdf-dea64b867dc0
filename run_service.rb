#!/usr/bin/env ruby

require_relative 'lib/route_manager'
require_relative 'lib/price_parser'
require_relative 'lib/telegram_bot'
require 'yaml'

class TicketMonitoringService
  def initialize
    @route_manager = RouteManager.new
    @price_parser = PriceParser.new
    @telegram_notifier = TelegramNotifier.new
    @running = false
  end

  def run
    puts "🛫 Сервис мониторинга цен на авиабилеты"
    puts "=" * 50
    
    # Проверяем конфигурацию
    config = YAML.load_file('config/config.yml')
    bot_token = config['telegram']['bot_token']
    
    if bot_token == "YOUR_BOT_TOKEN_HERE"
      puts "⚠️  Telegram бот не настроен. Работаем только с мониторингом цен."
      puts "   Для настройки бота см. TELEGRAM_SETUP.md"
      puts ""
      run_monitoring_only
    else
      puts "✅ Telegram бот настроен"
      puts ""
      show_menu
    end
  end

  private

  def show_menu
    loop do
      puts "\n📋 Выберите режим работы:"
      puts "1. 🤖 Запустить Telegram бота (интерактивный режим)"
      puts "2. 📊 Разовая проверка цен"
      puts "3. 🔄 Мониторинг с периодической проверкой"
      puts "4. 🚀 Запустить бота + мониторинг (рекомендуется)"
      puts "0. ❌ Выход"
      
      print "\nВведите номер: "
      choice = gets.chomp
      
      case choice
      when '1'
        run_bot_only
      when '2'
        run_single_check
      when '3'
        run_monitoring_only
      when '4'
        run_bot_and_monitoring
      when '0'
        puts "👋 До свидания!"
        break
      else
        puts "❌ Неверный выбор"
      end
    end
  end

  def run_bot_only
    puts "\n🤖 Запуск только Telegram бота..."
    @telegram_notifier.start_bot
  end

  def run_single_check
    puts "\n📊 Разовая проверка цен..."
    check_prices_once
  end

  def run_monitoring_only
    puts "\n🔄 Запуск мониторинга цен..."
    puts "⏰ Проверка каждые 2 часа"
    puts "🛑 Для остановки нажмите Ctrl+C"
    
    @running = true
    
    begin
      while @running
        check_prices_once
        
        if @running
          puts "\n⏰ Следующая проверка через 2 часа..."
          sleep_with_interrupt(2 * 60 * 60) # 2 часа
        end
      end
    rescue Interrupt
      puts "\n🛑 Остановка мониторинга..."
      @running = false
    end
  end

  def run_bot_and_monitoring
    puts "\n🚀 Запуск бота + мониторинга..."
    puts "🤖 Telegram бот: активен"
    puts "🔄 Мониторинг: каждые 2 часа"
    puts "🛑 Для остановки нажмите Ctrl+C"
    
    @running = true
    
    # Запускаем бота в отдельном потоке
    bot_thread = Thread.new do
      @telegram_notifier.start_bot
    end
    
    # Запускаем мониторинг в основном потоке
    begin
      while @running
        check_prices_once
        
        if @running
          puts "\n⏰ Следующая проверка через 2 часа..."
          sleep_with_interrupt(2 * 60 * 60) # 2 часа
        end
      end
    rescue Interrupt
      puts "\n🛑 Остановка сервиса..."
      @running = false
      @telegram_notifier.stop_bot
      bot_thread.kill
    end
  end

  def check_prices_once
    puts "\n🛫 Проверка цен на авиабилеты..."
    puts "⏰ #{Time.now.strftime('%d.%m.%Y %H:%M:%S')}"
    
    routes = @route_manager.get_valid_routes
    
    if routes.empty?
      puts "❌ Нет актуальных маршрутов для проверки"
      return
    end

    puts "📋 Проверяем #{routes.length} маршрутов..."

    price_data = []

    routes.each_with_index do |route, index|
      print "[#{index + 1}/#{routes.length}] #{route[:from]}-#{route[:to]} (#{route[:date_str]})... "
      
      price = @price_parser.get_price(route)
      
      price_data << {
        from: route[:from],
        to: route[:to],
        date_str: route[:date_str],
        price: price
      }

      if price
        puts "✅ #{price} руб."
      else
        puts "❌ не найдена"
      end

      # Небольшая пауза между запросами
      sleep(3) unless index == routes.length - 1
    end

    puts "\n📊 РЕЗУЛЬТАТЫ:"
    display_results_table(price_data)
    
    # Отправляем результаты в Telegram
    config = YAML.load_file('config/config.yml')
    if config['telegram']['bot_token'] != "YOUR_BOT_TOKEN_HERE" && !config['telegram']['chat_ids'].empty?
      puts "\n📱 Отправка в Telegram..."
      if @telegram_notifier.send_price_report(price_data)
        puts "✅ Отправлено #{config['telegram']['chat_ids'].length} подписчикам"
      else
        puts "❌ Ошибка отправки"
      end
    end
  end

  def display_results_table(price_data)
    # Группируем по маршрутам
    routes = price_data.group_by { |item| "#{item[:from]}-#{item[:to]}" }
    
    routes.each do |route, items|
      puts "\n🛫 #{route}:"
      puts "-" * 30
      
      items.each do |item|
        price_text = item[:price] ? "#{item[:price]} руб." : "Цена не найдена"
        status = item[:price] ? "✅" : "❌"
        puts "  #{status} #{item[:date_str].ljust(8)} | #{price_text}"
      end
    end
  end

  def sleep_with_interrupt(seconds)
    seconds.times do
      sleep(1)
      break unless @running
    end
  end
end

if __FILE__ == $0
  service = TicketMonitoringService.new
  service.run
end
